# 腾讯云COS配置
# 请将此文件复制为 .env 并填入真实的配置信息

# 腾讯云API密钥ID
COS_SECRET_ID=YOUR_SECRET_ID

# 腾讯云API密钥Key
COS_SECRET_KEY=YOUR_SECRET_KEY

# COS存储桶所在地域
COS_REGION=ap-beijing

# COS存储桶名称
COS_BUCKET_NAME=your-bucket-name

# CDN加速域名（可选）
COS_CDN_DOMAIN=https://www.gobaweb.com

# 文件访问URL有效期（秒）
COS_URL_EXPIRE_TIME=3600

# 文件上传最大大小（字节）
FILE_MAX_SIZE=104857600

# 文件存储路径前缀
FILE_PATH_PREFIX=tourism/files/

# 使用说明:
# 1. 复制此文件为 .env
# 2. 填入您的腾讯云COS配置信息
# 3. 运行 docker-compose up -d 启动服务
