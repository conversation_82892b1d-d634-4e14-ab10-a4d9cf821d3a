package com.tourism.service;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.model.ResponseHeaderOverrides;
import com.tourism.config.TencentCosConfig;
import com.tourism.dto.FileUploadResponseDTO;
import com.tourism.utils.FileUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URL;
import java.util.Date;

/**
 * 腾讯云COS服务类
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
public class CosService {

    @Autowired
    private COSClient cosClient;

    @Autowired
    private TencentCosConfig cosConfig;

    /**
     * 上传文件到COS
     *
     * @param file 文件
     * @param filePath 文件路径
     * @return 文件上传响应
     * @throws IOException IO异常
     */
    public FileUploadResponseDTO uploadFile(MultipartFile file, String filePath) throws IOException {
        try {
            // 设置对象元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            // 设置为inline，支持浏览器直接预览
            metadata.setContentDisposition("inline");

            // 设置缓存控制，提高预览性能
            metadata.setCacheControl("public, max-age=31536000"); // 1年缓存

            // 对于图片和视频文件，添加额外的预览支持
            String fileName = file.getOriginalFilename();
            if (fileName != null) {
                String extension = "";
                int lastDotIndex = fileName.lastIndexOf('.');
                if (lastDotIndex > 0) {
                    extension = fileName.substring(lastDotIndex + 1).toLowerCase();
                }

                // 确保正确的Content-Type用于预览
                if (isImageFile(extension)) {
                    if (metadata.getContentType() == null || metadata.getContentType().startsWith("application/")) {
                        metadata.setContentType("image/" + (extension.equals("jpg") ? "jpeg" : extension));
                    }
                } else if (isVideoFile(extension)) {
                    if (metadata.getContentType() == null || metadata.getContentType().startsWith("application/")) {
                        metadata.setContentType("video/" + extension);
                    }
                }
            }

            // 创建上传请求
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    cosConfig.getBucketName(),
                    filePath,
                    file.getInputStream(),
                    metadata
            );

            // 执行上传
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            log.info("文件上传成功，ETag: {}", putObjectResult.getETag());

            // 生成文件ID
            String fileId = IdUtil.simpleUUID();

            // 生成预览URL（优先使用CDN域名）
            String previewUrl = generatePreviewUrl(filePath);

            // 生成下载URL（使用预签名URL）
            String downloadUrl = generateDownloadUrlInternal(filePath);

            // 生成CDN URL（如果配置了CDN域名）
            String cdnUrl = null;
            if (StrUtil.isNotBlank(cosConfig.getCdnDomain())) {
                cdnUrl = cosConfig.getCdnDomain() + "/" + filePath;
            }

            // 构建响应对象
            return FileUploadResponseDTO.builder()
                    .fileId(fileId)
                    .originalFileName(file.getOriginalFilename())
                    .fileType(FileUtils.getFileType(file.getOriginalFilename()))
                    .fileSize(file.getSize())
                    .downloadUrl(downloadUrl)
                    .previewUrl(previewUrl)
                    .cdnUrl(cdnUrl)
                    .uploadTime(DateUtil.formatDateTime(new Date()))
                    .urlExpireTime(cosConfig.getUrlExpireTime())
                    .build();

        } catch (CosServiceException e) {
            log.error("COS服务异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getErrorMessage());
        } catch (CosClientException e) {
            log.error("COS客户端异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }



    /**
     * 生成文件预览URL
     *
     * @param filePath 文件路径
     * @return 预览URL
     */
    public String generatePreviewUrl(String filePath) {
        try {
            // 优先使用CDN域名作为预览URL（更适合预览）
            if (StrUtil.isNotBlank(cosConfig.getCdnDomain())) {
                // CDN URL通常支持直接预览，添加预览参数
                String cdnUrl = cosConfig.getCdnDomain() + "/" + filePath;
                // 对于图片和视频，CDN通常支持直接预览
                return addPreviewParameters(cdnUrl, filePath);
            }

            // 如果没有CDN域名，则生成预签名URL用于预览
            Date expiration = new Date(System.currentTimeMillis() + cosConfig.getUrlExpireTime() * 1000);

            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                    cosConfig.getBucketName(),
                    filePath,
                    HttpMethodName.GET
            );
            request.setExpiration(expiration);

            // ⭐️ 关键设置：使用 ResponseHeaderOverrides 强制浏览器预览而非下载 ⭐️
            ResponseHeaderOverrides headers = new ResponseHeaderOverrides();
            headers.setContentDisposition("inline"); // 设置为内联预览
            request.setResponseHeaders(headers);

            URL url = cosClient.generatePresignedUrl(request);
            return url.toString();

        } catch (Exception e) {
            log.error("生成预览URL失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成预览URL失败: " + e.getMessage());
        }
    }

    /**
     * 为CDN URL添加预览参数
     *
     * @param url 原始CDN URL
     * @param filePath 文件路径
     * @return 添加预览参数后的URL
     */
    private String addPreviewParameters(String url, String filePath) {
        // 获取文件扩展名
        String extension = "";
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filePath.substring(lastDotIndex + 1).toLowerCase();
        }

        // 对于图片文件，CDN通常直接支持预览，无需额外参数
        if (isImageFile(extension)) {
            // 对于腾讯云CDN，可以添加图片处理参数优化显示效果
            if (url.contains("myqcloud.com") || url.contains("tencentcloudapi.com")) {
                String separator = url.contains("?") ? "&" : "?";
                // 添加图片处理参数，优化预览效果
                return url + separator + "imageMogr2/quality/85";
            }
            // 其他CDN直接返回，通常支持图片预览
            return url;
        }

        // 对于视频文件，CDN通常也直接支持预览
        if (isVideoFile(extension)) {
            return url;
        }

        // 对于其他文件类型，尝试添加预览参数（CDN可能不支持）
        String separator = url.contains("?") ? "&" : "?";
        return url + separator + "response-content-disposition=inline";
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String extension) {
        return extension.matches("jpg|jpeg|png|gif|bmp|webp");
    }

    /**
     * 判断是否为视频文件
     */
    private boolean isVideoFile(String extension) {
        return extension.matches("mp4|avi|mov|wmv|flv|3gp|mkv");
    }

    /**
     * 生成文件下载URL（内部方法）
     *
     * @param filePath 文件路径
     * @return 下载URL
     */
    private String generateDownloadUrlInternal(String filePath) {
        try {
            // 设置URL过期时间
            Date expiration = new Date(System.currentTimeMillis() + cosConfig.getUrlExpireTime() * 1000);

            // 生成预签名URL请求
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                    cosConfig.getBucketName(),
                    filePath,
                    HttpMethodName.GET
            );
            request.setExpiration(expiration);

            // ⭐️ 设置为下载模式：使用 ResponseHeaderOverrides 强制浏览器下载 ⭐️
            ResponseHeaderOverrides headers = new ResponseHeaderOverrides();
            headers.setContentDisposition("attachment"); // 设置为附件下载
            request.setResponseHeaders(headers);

            // 生成预签名URL
            URL url = cosClient.generatePresignedUrl(request);
            return url.toString();

        } catch (Exception e) {
            log.error("生成下载URL失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成下载URL失败: " + e.getMessage());
        }
    }


}
