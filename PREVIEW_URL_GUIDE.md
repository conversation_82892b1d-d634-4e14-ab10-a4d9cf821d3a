# 文件预览URL使用指南

## 问题解决

### 为什么之前的预览URL会直接下载？

之前的预览URL实际上和下载URL是相同的，都会触发浏览器下载文件。现在我们已经修复了这个问题。

## 修复内容

### 1. 上传时的元数据设置
- 设置 `Content-Disposition: inline` 确保浏览器预览而不是下载
- 正确设置 `Content-Type` 确保浏览器识别文件类型
- 添加缓存控制提高预览性能

### 2. 预览URL生成策略
- **CDN域名优先**: 如果配置了CDN域名，优先使用CDN URL
- **图片处理**: 对腾讯云COS图片添加处理参数
- **预览参数**: 添加 `response-content-disposition=inline` 参数

### 3. 不同文件类型的处理
- **图片文件**: 直接预览，支持格式转换
- **视频文件**: 添加正确的Content-Type确保预览
- **其他文件**: 添加inline参数尝试预览

## 使用方法

### 1. 上传文件
```bash
curl -X POST \
  http://localhost:8080/api/test/upload \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@test-image.jpg'
```

### 2. 响应示例
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "fileId": "abc123",
    "originalFileName": "test-image.jpg",
    "fileType": "image",
    "fileSize": 102400,
    "downloadUrl": "https://bucket.cos.ap-beijing.myqcloud.com/path/file.jpg",
    "previewUrl": "https://cdn.domain.com/path/file.jpg?response-content-disposition=inline",
    "cdnUrl": "https://cdn.domain.com/path/file.jpg",
    "uploadTime": "2023-12-01 12:34:56",
    "urlExpireTime": 3600
  }
}
```

### 3. URL区别说明

| URL类型 | 用途 | 浏览器行为 |
|---------|------|------------|
| downloadUrl | 文件下载 | 触发下载对话框 |
| previewUrl | 文件预览 | 直接在浏览器中显示 |
| cdnUrl | CDN访问 | 根据文件类型决定 |

## 测试方法

### 1. 浏览器测试
1. 上传一个图片文件
2. 复制响应中的 `previewUrl`
3. 在浏览器中打开该URL
4. 应该直接显示图片，而不是下载

### 2. 对比测试
1. 同时打开 `downloadUrl` 和 `previewUrl`
2. `downloadUrl` 应该触发下载
3. `previewUrl` 应该直接显示

## 配置建议

### 1. CDN配置
```yaml
tencent:
  cos:
    cdn-domain: https://your-cdn-domain.com
```

### 2. 腾讯云COS设置
- 确保存储桶允许公共读取（如果需要）
- 配置CORS允许跨域访问
- 启用CDN加速

### 3. 浏览器兼容性
- 现代浏览器都支持inline预览
- 移动端浏览器可能有不同行为
- 建议提供降级方案

## 故障排除

### 1. 仍然触发下载
- 检查CDN配置是否正确
- 确认文件Content-Type设置
- 查看浏览器开发者工具中的响应头

### 2. 预览失败
- 检查文件格式是否支持预览
- 确认URL参数是否正确添加
- 查看网络请求是否成功

### 3. CDN缓存问题
- 清除CDN缓存
- 等待缓存更新
- 使用版本参数强制刷新
