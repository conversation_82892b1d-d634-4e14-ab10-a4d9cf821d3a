package com.tourism.controller;

import com.tourism.common.Result;
import com.tourism.dto.FileUploadResponseDTO;
import com.tourism.service.FileManagementService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件上传测试控制器（专门用于API文档测试）
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Api(tags = "文件上传测试接口", description = "专门用于在API文档中测试文件上传功能")
public class FileUploadTestController {

    @Autowired
    private FileManagementService fileManagementService;

    /**
     * 简单文件上传测试
     *
     * @param file 文件
     * @return 上传结果
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(
            value = "文件上传测试",
            notes = "专门用于API文档测试的文件上传接口，支持图片和视频文件",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "上传成功"),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器错误")
    })
    public Result<FileUploadResponseDTO> testUpload(
            @ApiParam(value = "要上传的文件", required = true, type = "file")
            @RequestPart("file") MultipartFile file) {

        try {
            log.info("API文档测试 - 接收到文件上传请求: {}", file.getOriginalFilename());

            if (file.isEmpty()) {
                return Result.badRequest("文件不能为空");
            }

            FileUploadResponseDTO response = fileManagementService.uploadFile(file);

            return Result.success("文件上传成功", response);

        } catch (IllegalArgumentException e) {
            log.warn("文件上传参数错误: {}", e.getMessage());
            return Result.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("文件上传异常: {}", e.getMessage(), e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }



    /**
     * 获取支持的文件格式
     *
     * @return 支持的文件格式
     */
    @GetMapping("/supported-formats")
    @ApiOperation(value = "获取支持的文件格式", notes = "返回系统支持的图片和视频文件格式")
    public Result<Object> getSupportedFormats() {
        return Result.success("支持的文件格式", new Object() {
            public final String[] imageFormats = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
            public final String[] videoFormats = {"mp4", "avi", "mov", "wmv", "flv", "3gp", "mkv"};
            public final String maxFileSize = "1000MB";
            public final String note = "请选择支持的格式进行上传测试";
        });
    }
}
