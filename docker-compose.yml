version: '3.8'

services:
  tourism-file-api:
    build:
      context: .
      dockerfile: Dockerfile
    image: tourism/file-management-api:1.0.0
    container_name: tourism-file-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Spring配置
      - SPRING_PROFILES_ACTIVE=docker
      - SERVER_PORT=8080
      
      # 腾讯云COS配置（请替换为实际值）
      - TENCENT_COS_SECRET_ID=${COS_SECRET_ID:-YOUR_SECRET_ID}
      - TENCENT_COS_SECRET_KEY=${COS_SECRET_KEY:-YOUR_SECRET_KEY}
      - TENCENT_COS_REGION=${COS_REGION:-ap-beijing}
      - TENCENT_COS_BUCKET_NAME=${COS_BUCKET_NAME:-your-bucket-name}
      - TENCENT_COS_CDN_DOMAIN=${COS_CDN_DOMAIN:-https://www.gobaweb.com}
      - TENCENT_COS_URL_EXPIRE_TIME=${COS_URL_EXPIRE_TIME:-3600}
      
      # 文件上传配置
      - FILE_UPLOAD_MAX_SIZE=${FILE_MAX_SIZE:-104857600}
      - FILE_UPLOAD_PATH_PREFIX=${FILE_PATH_PREFIX:-tourism/files/}
      
      # JVM配置
      - JAVA_OPTS=-Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Duser.timezone=GMT+08
    volumes:
      - ./logs:/app/logs
    networks:
      - tourism-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/health/check"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  tourism-network:
    driver: bridge

# 使用示例:
# 1. 创建 .env 文件并设置环境变量
# 2. 运行: docker-compose up -d
# 3. 查看日志: docker-compose logs -f
# 4. 停止服务: docker-compose down
