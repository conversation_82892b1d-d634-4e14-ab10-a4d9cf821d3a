server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: tourism-file-management-api
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher


# 腾讯云COS配置
tencent:
  cos:
    # 请替换为您的实际配置
    secret-id: AKIDTntlQs55c1C9sNgkJgqmTxEG3cx3uYyV
    secret-key: PZVIrCkBWiombGYgeS6av67zhGfuzviV
    region: ap-guangzhou
    bucket-name: travel-1315014578
    # CDN域名（可选，用于加速访问）
    cdn-domain: 
    # 文件访问有效期（秒）
    url-expire-time: 1576800000

# Swagger配置
springfox:
  documentation:
    swagger-ui:
      enabled: true

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: true
    enable-request-cache: true
    enable-host: false
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
  production: false
  cors: true

# 日志配置
logging:
  level:
    com.tourism: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/tourism-file-api.log

# 文件上传配置
file:
  upload:
    # 允许的图片格式
    image-types: jpg,jpeg,png,gif,bmp,webp
    # 允许的视频格式
    video-types: mp4,avi,mov,wmv,flv,3gp,mkv
    # 最大文件大小（字节）
    max-size: 1048576000  # 1000MB
    # 文件存储路径前缀
    path-prefix: tourism/files/
